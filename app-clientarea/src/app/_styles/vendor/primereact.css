.p-datatable {
    color: theme(colors[text-primary]);
    height: inherit;
}

.p-datatable > .p-datatable-wrapper {
    height: 100%;
}

.p-datatable .p-datatable-thead > tr > th { 
    position: relative;
    background-color: transparent;
    font-size: 14px; 
    line-height: 1rem;
    font-weight: 500;
}

.p-column-header-content {
    display: flex;
    position: relative;
}

.p-column-resizer {
    width: 2px;
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    cursor: col-resize;
    z-index: 1;
    background-color: transparent; 
}

/* Needed for the stickyu header to work with borders. Normal borders lost when scrolling */
.pr_dashboard .p-datatable .p-datatable-thead::before,
.pr_dashboard .p-datatable .p-datatable-thead::after {
    content: '';
    position: absolute;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: theme(colors[layout-divider]);
    margin-top: -1px;
}

.p-datatable .p-datatable-thead > tr > th {
    background-color: white;
}

.pr_survey .p-datatable .p-datatable-thead > tr > th {
    border-bottom: solid 1px theme(colors[layout-divider]);
}

.p-datatable .p-datatable-thead > tr > th.p-highlight { 
    color: inherit;
}

.p-datatable .p-datatable-thead > tr > th svg {
    width: 10px;
}

.p-datatable .p-datatable-thead > tr > th.p-highlight svg {
    color: inherit;
}

/* Table > Body > Rows */
.p-datatable .p-datatable-tbody > tr {
    border-bottom: solid 1px theme(colors[layout-divider]);
}

.p-datatable .p-datatable-tbody > tr:hover {
    background-color: theme(colors[surface-neutral-200]);
}

.pr_dashboard .p-datatable .p-datatable-tbody > tr:hover > td {
    font-weight: 600;
}

.pr_dashboard_contact .p-datatable .p-datatable-tbody > tr:hover {
    background-color: #fff;
    font-weight: 400;
}

.pr_dashboard_contact .p-datatable .p-datatable-tbody > tr:hover > td {
    font-weight: normal;
}

.p-datatable .p-datatable-tbody > tr.p-highlight {
    background-color: transparent;
    color: inherit;
}

.p-datatable .p-datatable-tbody > tr.p-highlight:hover {
    background-color: theme(colors[surface-neutral-200]);
}

.p-datatable .p-datatable-tbody > tr.invalid,
.p-datatable .p-datatable-tbody > tr.invalid > td {
    background-color: theme(colors[invalid-row]);
}

.p-datatable .p-datatable-frozen-view .tr.invalid,
.p-datatable .p-datatable-frozen-view .tr.invalid > td {
    background-color: theme(colors[invalid-row]);
}

/* Table > Body > Rows > Cells */
.p-datatable .p-datatable-tbody > tr > td {
    padding: 1rem;
    font-size: 14px;
    line-height: 1rem;
    background-color: theme(colors[surface-neutral-100]);
}

/* Table > Body > Row Groupings */
.p-datatable .p-datatable-tbody > tr.p-rowgroup-header {
    position: static;
    border-bottom: 0;
}

.p-datatable .p-datatable-tbody > tr.p-rowgroup-header > td {
    padding-bottom: 0;
}

/* Table Selection */
.p-datatable .p-datatable-thead > tr > th .p-checkbox,
.p-datatable .p-datatable-tbody > tr > td .p-checkbox {
    background-color: transparent;
    width: 1.125rem;
    height: 1.125rem;
}

.p-datatable .p-datatable-thead > tr > th .p-checkbox > .p-checkbox-box, 
.p-datatable .p-datatable-tbody > tr > td .p-checkbox > .p-checkbox-box {
    background-color: transparent;
    border-radius: 2px;
    border: solid 2px theme(colors[secondary]);
    width: 1.125rem;
    height: 1.125rem;
}

.p-datatable .p-datatable-thead > tr > th .p-checkbox.p-highlight > .p-checkbox-box,
.p-datatable .p-datatable-tbody > tr > td .p-checkbox.p-highlight > .p-checkbox-box {
    background-color: theme(colors[secondary]);
}

.p-datatable .p-datatable-thead > tr > th .p-checkbox > .p-checkbox-box {
    display: none;
}

.p-datatable .p-datatable-thead > tr > th .p-column-header-content {
    display: flex;
}

/* Filters */
.p-column-filter button.p-column-filter-menu-button {
    color: theme(colors[surface-neutral-400]);
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 3px;
}

.p-column-filter button.p-column-filter-menu-button-active {
    background-color: theme(colors[success]);
    color: #FFF;
}

.p-column-filter-overlay {
    border-radius: 0;
}

.p-column-filter-overlay .p-column-filter-constraints {
    font-size: 0.75rem;
}

.p-column-filter-overlay .p-column-filter-constraints .p-column-filter-constraint .p-dropdown-label {
    font-size: 0.75rem;
}

.p-column-filter-overlay .p-column-filter-constraints .p-column-filter-constraint .p-dropdown-trigger svg {
    width: 10px;
    height: 10px;
}

.p-column-filter-overlay .p-column-filter-constraints .p-column-filter-matchmode-dropdown {
    border: solid 1px theme(colors[surface-neutral-300]);
    border-radius: 0;
}

.p-column-filter-overlay .p-column-filter-constraints .p-dropdown:not(.p-disabled).p-focus {
    box-shadow: none;
    border-color: none;
}

.p-column-filter-overlay .p-column-filter-constraints .p-column-filter.p-inputtext {
    border: solid 1px theme(colors[surface-neutral-300]);
    border-radius: 0;
    padding: 12px 14px;
}

.p-dropdown-panel {
    border-radius: 0;
}

.p-dropdown-panel .p-dropdown-items li:hover {
    background-color: theme(colors[primary]);
}

.p-dropdown-panel .p-dropdown-items .p-dropdown-item.p-highlight.p-focus,
.p-dropdown-panel .p-dropdown-items .p-dropdown-item.p-highlight {
    color: theme(colors[secondary]);
    font-weight: 600;
    background: theme(colors[primary]);
}

.p-dropdown-panel .p-dropdown-items li .p-dropdown-item-label {
    font-size: 0.75rem;
}

.p-column-filter-overlay .p-column-filter-buttonbar button {
    font-size: 0.75rem;
}

/* Pagination */
.pr_survey .p-datatable div.p-paginator {
    color: theme(colors[text-primary]);
    font-size: 0.9rem;
}

.pr_survey .p-datatable div.p-paginator .p-dropdown {
    outline: none;
    box-shadow: none;
}

.pr_survey .p-datatable div.p-paginator .p-dropdown-label {
    color: theme(colors[text-primary]);
    font-size: 0.9rem;
}

.pr_dashboard .p-datatable div.p-paginator {
    display: none !important;
    visibility: hidden !important;
    color: theme(colors[text-primary]);
    font-size: 0.75rem;
    padding: 0.5rem 1rem 0 1rem;
}

.pr_dashboard .p-datatable .p-paginator .p-paginator-current,
.pr_dashboard .p-datatable .p-paginator .p-paginator-prev,
.pr_dashboard .p-datatable .p-paginator .p-paginator-next {
    height: 1.5rem;
}

.p-tooltip .p-tooltip-text {
    background: #ffffff;
    color: #000000;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.p-datatable-scrollable .p-datatable-thead > tr > th.p-frozen-column {
    position: sticky;
    left: 0;
    z-index: 3; /* must be >2 to stay above body cells */
    background-color: theme(colors[surface-neutral-100]); /* or white */
    border-bottom: solid 1px theme(colors[layout-divider]);
  }

.p-datatable-scrollable .p-frozen-column {
    position: sticky;
    left: 0;
    z-index: 2; /* ensures it's above scrollable headers */
    background-color: theme(colors[surface-neutral-100]);
}

.p-datatable-scrollable-thead > tr > th {
    background-color: theme(colors[surface-neutral-100]);
    z-index: 1;
}

/* Conditional scroll styles */
.p-datatable-wrapper.scroll-disabled {
    cursor: not-allowed;
}

.p-datatable-wrapper.scroll-disabled * {
    pointer-events: none;
}

.p-datatable-wrapper.scroll-disabled .p-datatable-tbody > tr {
    pointer-events: auto;
}

.p-datatable-wrapper.scroll-disabled .p-datatable-thead > tr > th {
    pointer-events: auto;
}
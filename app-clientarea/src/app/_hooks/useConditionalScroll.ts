import { useEffect, useRef, useState, useCallback } from 'react';

interface UseConditionalScrollOptions {
  /**
   * Radius from the center of the table where scrolling is allowed (in pixels)
   * Default: 100
   */
  radius?: number;
  /**
   * Whether the conditional scrolling is enabled
   * Default: true
   */
  enabled?: boolean;
}

interface UseConditionalScrollReturn {
  containerRef: React.RefObject<HTMLDivElement>;
  isScrollEnabled: boolean;
}

export function useConditionalScroll(options: UseConditionalScrollOptions = {}): UseConditionalScrollReturn {
  const { radius = 100, enabled = true } = options;
  const containerRef = useRef<HTMLDivElement>(null);
  const [isScrollEnabled, setIsScrollEnabled] = useState(true);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  const handleMouseMove = useCallback((event: MouseEvent) => {
    if (!enabled || !containerRef.current) return;

    const container = containerRef.current;
    const rect = container.getBoundingClientRect();
    
    // Calculate mouse position relative to the container
    const relativeX = event.clientX - rect.left;
    const relativeY = event.clientY - rect.top;
    
    // Calculate center of the container
    const centerX = rect.width / 2;
    const centerY = rect.height / 2;
    
    // Calculate distance from center
    const distanceFromCenter = Math.sqrt(
      Math.pow(relativeX - centerX, 2) + Math.pow(relativeY - centerY, 2)
    );
    
    // Enable scrolling only if cursor is within the radius from center
    const shouldEnableScroll = distanceFromCenter <= radius;
    
    setMousePosition({ x: relativeX, y: relativeY });
    setIsScrollEnabled(shouldEnableScroll);
  }, [radius, enabled]);

  const handleMouseLeave = useCallback(() => {
    if (!enabled) return;
    // Disable scrolling when mouse leaves the container
    setIsScrollEnabled(false);
  }, [enabled]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container || !enabled) return;

    // Find the scrollable wrapper inside the datatable
    const wrapper = container.querySelector('.p-datatable-wrapper') as HTMLElement;
    if (!wrapper) return;

    // Add event listeners to the container
    container.addEventListener('mousemove', handleMouseMove);
    container.addEventListener('mouseleave', handleMouseLeave);

    // Apply scroll behavior to the wrapper
    const updateScrollBehavior = () => {
      if (isScrollEnabled) {
        wrapper.style.overflowX = 'auto';
        wrapper.style.pointerEvents = 'auto';
      } else {
        wrapper.style.overflowX = 'hidden';
        wrapper.style.pointerEvents = 'none';
      }
    };

    updateScrollBehavior();

    return () => {
      container.removeEventListener('mousemove', handleMouseMove);
      container.removeEventListener('mouseleave', handleMouseLeave);
      
      // Reset scroll behavior on cleanup
      if (wrapper) {
        wrapper.style.overflowX = 'auto';
        wrapper.style.pointerEvents = 'auto';
      }
    };
  }, [handleMouseMove, handleMouseLeave, isScrollEnabled, enabled]);

  // If not enabled, always allow scrolling
  if (!enabled) {
    return {
      containerRef,
      isScrollEnabled: true,
    };
  }

  return {
    containerRef,
    isScrollEnabled,
  };
}

import { useEffect, useRef, useState, useCallback } from 'react';

interface UseConditionalScrollOptions {
  /**
   * Radius from the center of the table where scrolling is allowed (in pixels)
   * Default: 100
   */
  radius?: number;
  /**
   * Whether the conditional scrolling is enabled
   * Default: true
   */
  enabled?: boolean;
}

interface UseConditionalScrollReturn {
  containerRef: React.RefObject<HTMLDivElement>;
  isScrollEnabled: boolean;
  showScrollIndicator: boolean;
  scrollIndicatorStyle: React.CSSProperties;
}

export function useConditionalScroll(options: UseConditionalScrollOptions = {}): UseConditionalScrollReturn {
  const { radius = 100, enabled = true } = options;
  const containerRef = useRef<HTMLDivElement>(null);
  const [isScrollEnabled, setIsScrollEnabled] = useState(true);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [containerDimensions, setContainerDimensions] = useState({ width: 0, height: 0 });
  const [showScrollIndicator, setShowScrollIndicator] = useState(false);

  const handleMouseMove = useCallback((event: MouseEvent) => {
    if (!enabled || !containerRef.current) return;

    const container = containerRef.current;
    const rect = container.getBoundingClientRect();

    // Update container dimensions
    setContainerDimensions({ width: rect.width, height: rect.height });

    // Calculate mouse position relative to the container
    const relativeX = event.clientX - rect.left;
    const relativeY = event.clientY - rect.top;

    // Calculate center of the container
    const centerX = rect.width / 2;
    const centerY = rect.height / 2;

    // Calculate distance from center
    const distanceFromCenter = Math.sqrt(
      Math.pow(relativeX - centerX, 2) + Math.pow(relativeY - centerY, 2)
    );

    // Enable scrolling only if cursor is within the radius from center
    const shouldEnableScroll = distanceFromCenter <= radius;

    // Debug logging
    if (process.env.NODE_ENV === 'development') {
      console.log('Conditional Scroll:', {
        distanceFromCenter: Math.round(distanceFromCenter),
        radius,
        shouldEnableScroll,
        mousePos: { x: Math.round(relativeX), y: Math.round(relativeY) },
        center: { x: Math.round(centerX), y: Math.round(centerY) }
      });
    }

    setMousePosition({ x: relativeX, y: relativeY });
    setIsScrollEnabled(shouldEnableScroll);
  }, [radius, enabled]);

  const handleMouseEnter = useCallback(() => {
    if (!enabled) return;
    setShowScrollIndicator(true);
  }, [enabled]);

  const handleMouseLeave = useCallback(() => {
    if (!enabled) return;
    // Disable scrolling when mouse leaves the container
    setIsScrollEnabled(false);
    setShowScrollIndicator(false);
  }, [enabled]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container || !enabled) return;

    // Find the scrollable wrapper inside the datatable
    const wrapper = container.querySelector('.p-datatable-wrapper') as HTMLElement;
    if (!wrapper) return;

    // Prevent wheel events on container when scrolling is disabled
    const handleContainerWheel = (e: WheelEvent) => {
      if (!isScrollEnabled && (e.deltaX !== 0)) {
        e.preventDefault();
        e.stopPropagation();
      }
    };

    // Add event listeners to the container
    container.addEventListener('mouseenter', handleMouseEnter);
    container.addEventListener('mousemove', handleMouseMove);
    container.addEventListener('mouseleave', handleMouseLeave);
    container.addEventListener('wheel', handleContainerWheel, { passive: false });

    return () => {
      container.removeEventListener('mouseenter', handleMouseEnter);
      container.removeEventListener('mousemove', handleMouseMove);
      container.removeEventListener('mouseleave', handleMouseLeave);
      container.removeEventListener('wheel', handleContainerWheel);
    };
  }, [handleMouseEnter, handleMouseMove, handleMouseLeave, enabled, isScrollEnabled]);

  // Separate effect to handle scroll behavior changes
  useEffect(() => {
    const container = containerRef.current;
    if (!container || !enabled) return;

    const wrapper = container.querySelector('.p-datatable-wrapper') as HTMLElement;
    if (!wrapper) return;

    // Prevent wheel events when scrolling is disabled
    const handleWheel = (e: WheelEvent) => {
      if (!isScrollEnabled && (e.deltaX !== 0)) {
        e.preventDefault();
        e.stopPropagation();
        return false;
      }
    };

    // Prevent actual scrolling by resetting scroll position
    const handleScroll = (e: Event) => {
      if (!isScrollEnabled) {
        const target = e.target as HTMLElement;
        target.scrollLeft = 0;
        e.preventDefault();
        e.stopPropagation();
        return false;
      }
    };

    // Prevent touch events when scrolling is disabled
    const handleTouchMove = (e: TouchEvent) => {
      if (!isScrollEnabled) {
        e.preventDefault();
        e.stopPropagation();
      }
    };

    // Prevent keyboard scroll events when scrolling is disabled
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isScrollEnabled && (e.key === 'ArrowLeft' || e.key === 'ArrowRight')) {
        e.preventDefault();
        e.stopPropagation();
      }
    };

    // Apply scroll behavior to the wrapper
    if (isScrollEnabled) {
      wrapper.style.overflowX = 'auto';
      wrapper.classList.remove('scroll-disabled');
      wrapper.removeEventListener('wheel', handleWheel);
      wrapper.removeEventListener('touchmove', handleTouchMove);
      wrapper.removeEventListener('keydown', handleKeyDown);
      wrapper.removeEventListener('scroll', handleScroll);
    } else {
      wrapper.style.overflowX = 'hidden';
      wrapper.classList.add('scroll-disabled');
      wrapper.addEventListener('wheel', handleWheel, { passive: false });
      wrapper.addEventListener('touchmove', handleTouchMove, { passive: false });
      wrapper.addEventListener('keydown', handleKeyDown);
      wrapper.addEventListener('scroll', handleScroll, { passive: false });
    }

    return () => {
      // Reset scroll behavior on cleanup
      wrapper.style.overflowX = 'auto';
      wrapper.classList.remove('scroll-disabled');
      wrapper.removeEventListener('wheel', handleWheel);
      wrapper.removeEventListener('touchmove', handleTouchMove);
      wrapper.removeEventListener('keydown', handleKeyDown);
      wrapper.removeEventListener('scroll', handleScroll);
    };
  }, [isScrollEnabled, enabled]);

  // Calculate scroll indicator style
  const scrollIndicatorStyle: React.CSSProperties = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: `${radius * 2}px`,
    height: `${radius * 2}px`,
    borderRadius: '50%',
    backgroundColor: isScrollEnabled
      ? 'rgba(34, 197, 94, 0.15)' // Green when scroll enabled
      : 'rgba(239, 68, 68, 0.15)', // Red when scroll disabled
    border: isScrollEnabled
      ? '2px dashed rgba(34, 197, 94, 0.4)'
      : '2px dashed rgba(239, 68, 68, 0.4)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    pointerEvents: 'none',
    zIndex: 10,
    fontSize: '14px',
    color: isScrollEnabled
      ? 'rgba(34, 197, 94, 0.8)'
      : 'rgba(239, 68, 68, 0.8)',
    fontWeight: '600',
    textAlign: 'center',
    lineHeight: '1.2',
    whiteSpace: 'pre-line',
    transition: 'all 0.2s ease-in-out',
  };

  // If not enabled, always allow scrolling
  if (!enabled) {
    return {
      containerRef,
      isScrollEnabled: true,
      showScrollIndicator: false,
      scrollIndicatorStyle: {},
    };
  }

  return {
    containerRef,
    isScrollEnabled,
    showScrollIndicator,
    scrollIndicatorStyle,
  };
}

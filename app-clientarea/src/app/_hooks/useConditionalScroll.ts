import { useEffect, useRef, useState, useCallback } from 'react';

interface UseConditionalScrollOptions {
  /**
   * Radius from the center of the table where scrolling is allowed (in pixels)
   * Default: 100
   */
  radius?: number;
  /**
   * Whether the conditional scrolling is enabled
   * Default: true
   */
  enabled?: boolean;
}

interface UseConditionalScrollReturn {
  containerRef: React.RefObject<HTMLDivElement>;
  isScrollEnabled: boolean;
  showScrollIndicator: boolean;
  scrollIndicatorStyle: React.CSSProperties;
}

export function useConditionalScroll(options: UseConditionalScrollOptions = {}): UseConditionalScrollReturn {
  const { radius = 100, enabled = true } = options;
  const containerRef = useRef<HTMLDivElement>(null);
  const [isScrollEnabled, setIsScrollEnabled] = useState(true);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [containerDimensions, setContainerDimensions] = useState({ width: 0, height: 0 });
  const [showScrollIndicator, setShowScrollIndicator] = useState(false);

  const handleMouseMove = useCallback((event: MouseEvent) => {
    if (!enabled || !containerRef.current) return;

    const container = containerRef.current;
    const rect = container.getBoundingClientRect();

    // Update container dimensions
    setContainerDimensions({ width: rect.width, height: rect.height });

    // Calculate mouse position relative to the container
    const relativeX = event.clientX - rect.left;
    const relativeY = event.clientY - rect.top;

    // Calculate center of the container
    const centerX = rect.width / 2;
    const centerY = rect.height / 2;

    // Calculate distance from center
    const distanceFromCenter = Math.sqrt(
      Math.pow(relativeX - centerX, 2) + Math.pow(relativeY - centerY, 2)
    );

    // Enable scrolling only if cursor is within the radius from center
    const shouldEnableScroll = distanceFromCenter <= radius;

    setMousePosition({ x: relativeX, y: relativeY });
    setIsScrollEnabled(shouldEnableScroll);
  }, [radius, enabled]);

  const handleMouseEnter = useCallback(() => {
    if (!enabled) return;
    setShowScrollIndicator(true);
  }, [enabled]);

  const handleMouseLeave = useCallback(() => {
    if (!enabled) return;
    // Disable scrolling when mouse leaves the container
    setIsScrollEnabled(false);
    setShowScrollIndicator(false);
  }, [enabled]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container || !enabled) return;

    // Find the scrollable wrapper inside the datatable
    const wrapper = container.querySelector('.p-datatable-wrapper') as HTMLElement;
    if (!wrapper) return;

    // Add event listeners to the container
    container.addEventListener('mouseenter', handleMouseEnter);
    container.addEventListener('mousemove', handleMouseMove);
    container.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      container.removeEventListener('mouseenter', handleMouseEnter);
      container.removeEventListener('mousemove', handleMouseMove);
      container.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [handleMouseEnter, handleMouseMove, handleMouseLeave, enabled]);

  // Separate effect to handle scroll behavior changes
  useEffect(() => {
    const container = containerRef.current;
    if (!container || !enabled) return;

    const wrapper = container.querySelector('.p-datatable-wrapper') as HTMLElement;
    if (!wrapper) return;

    // Store original overflow style
    const originalOverflowX = wrapper.style.overflowX || 'auto';

    // Apply scroll behavior to the wrapper
    if (isScrollEnabled) {
      wrapper.style.overflowX = originalOverflowX;
      wrapper.classList.remove('scroll-disabled');
    } else {
      wrapper.style.overflowX = 'hidden';
      wrapper.classList.add('scroll-disabled');
    }

    return () => {
      // Reset scroll behavior on cleanup
      wrapper.style.overflowX = originalOverflowX;
      wrapper.classList.remove('scroll-disabled');
    };
  }, [isScrollEnabled, enabled]);

  // Calculate scroll indicator style
  const scrollIndicatorStyle: React.CSSProperties = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: `${radius * 2}px`,
    height: `${radius * 2}px`,
    borderRadius: '50%',
    backgroundColor: 'rgba(59, 130, 246, 0.1)', // Blue with low opacity
    border: '2px dashed rgba(59, 130, 246, 0.3)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    pointerEvents: 'none',
    zIndex: 10,
    fontSize: '12px',
    color: 'rgba(59, 130, 246, 0.7)',
    fontWeight: '500',
    textAlign: 'center',
    lineHeight: '1.2',
    whiteSpace: 'pre-line',
  };

  // If not enabled, always allow scrolling
  if (!enabled) {
    return {
      containerRef,
      isScrollEnabled: true,
      showScrollIndicator: false,
      scrollIndicatorStyle: {},
    };
  }

  return {
    containerRef,
    isScrollEnabled,
    showScrollIndicator,
    scrollIndicatorStyle,
  };
}

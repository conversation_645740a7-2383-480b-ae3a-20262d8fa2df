"use client";

import { useState, useEffect, useRef } from "react";
import { usePanel } from "@/_providers/PanelContext";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { FilePenLine } from "lucide-react";
import EditPanelMember from "./EditPanelMember";
import { translate } from "@/_utils/translate";
import { cn } from "@/_utils/tailwind";
import { BadgeCheck } from "lucide-react";
import { useMediaQuery } from "react-responsive";
import { useConditionalScroll } from "@/_hooks/useConditionalScroll";


const PanelTable = ({
  dt,
  dtRef,
  panel,
  selectedPanel,
  setSelectedPanel,
  contactTypes,
  contactSeniority,
  contactJobFunction,
  countries,
  cities,
  languages,
  vertical,
}) => {
  const { updatePanelMember, filters, setUnsavedChanges } = usePanel();
  const [editPanelMember, setEditPanelMember] = useState(null);
  const [panelMemberSheet, setPanelMemberSheet] = useState(false);
  const [showLeftShadow, setShowLeftShadow] = useState(false);
  const [showRightShadow, setShowRightShadow] = useState(false);
  const [hoveredRowKey, setHoveredRowKey] = useState(null);
  const [hoverY, setHoverY] = useState(0);
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const hoverDelayRef = useRef<NodeJS.Timeout | null>(null);
  const isMobile = useMediaQuery({ query: "(max-width: 1024px)" });

  // Use conditional scroll hook with 150px radius from center
  const { containerRef, isScrollEnabled } = useConditionalScroll({
    radius: 150,
    enabled: !isMobile // Disable on mobile to preserve normal scroll behavior
  });

  useEffect(() => {
    const parent = containerRef.current;
    if (!parent) return;

    // Find the inner scrollable element
    const wrapper = parent.querySelector(".p-datatable-wrapper");
    const table = wrapper?.querySelector("table");
    if (!wrapper) return;

    const handleScroll = () => {
      const canScrollLeft = wrapper.scrollLeft > 0;
      const canScrollRight = wrapper.scrollLeft + wrapper.clientWidth < wrapper.scrollWidth - 3;

      setShowLeftShadow(false);
      if (isMobile) {
        setShowLeftShadow(canScrollLeft);
      }
      setShowRightShadow(canScrollRight);
    };

    handleScroll();

    wrapper.addEventListener("scroll", handleScroll);
    window.addEventListener("resize", handleScroll);
    const resizeObserver = new ResizeObserver(handleScroll);

    if (table) {
      resizeObserver.observe(table);
    }

    return () => {
      wrapper.removeEventListener("scroll", handleScroll);
      window.removeEventListener("resize", handleScroll);
      resizeObserver.disconnect();
    };
  }, [panel, isMobile]);

  const setPanelMemberSheetState = (row) => {
    setEditPanelMember(row);
    setPanelMemberSheet((state) => !state);
  };

  const onRowSelect = (event) => {
    const panelMember = event.data;
    panelMember.selected = true;
    updatePanelMember(panelMember);
  };

  const onRowUnselect = (event) => {
    const panelMember = event.data;
    panelMember.selected = false;
    updatePanelMember(panelMember);
  };

  const updateInternalPanel = (value) => {
    const filteredPanel = panel.filter((member) => member.selected === true);
    setSelectedPanel(filteredPanel);
    setUnsavedChanges(true);
  };

  const nonResponderTemplate = (row) => {
    return (
      <>
        {row.serial_non_responder === true && (
          <div className="flex items-center justify-center cursor-pointer">
            <BadgeCheck className="h-4 w-4" />
          </div>
        )}
      </>
    );
  };

  return (
    <div
      ref={containerRef}
      className={cn(
        "relative overflow-x-auto",
        showLeftShadow && "has-left-shadow",
        showRightShadow && "has-right-shadow"
      )}
      onMouseEnter={() => {
        if (hoverTimeoutRef.current) {
          clearTimeout(hoverTimeoutRef.current);
        }
      }}
      onMouseLeave={() => {
        if (hoverDelayRef.current) clearTimeout(hoverDelayRef.current);
        setHoveredRowKey(null);
      }}       
    >
      <EditPanelMember
        panelMember={editPanelMember}
        contactTypes={contactTypes}
        contactSeniority={contactSeniority}
        contactJobFunction={contactJobFunction}
        countries={countries}
        cities={cities}
        languages={languages}
        vertical={vertical}
        openState={panelMemberSheet}
        setOpenState={setPanelMemberSheet}
      />

      <DataTable
        ref={dtRef}
        value={panel}
        selectionMode="checkbox"
        selection={selectedPanel}
        onSelectionChange={(e) => updateInternalPanel(e.value)}
        onRowSelect={onRowSelect}
        onRowUnselect={onRowUnselect}
        rowClassName={(rowData) => 
          rowData.key === hoveredRowKey ? "hovered-row" : ""
        }
        onRowMouseEnter={(e) => {
          const rowElement = e.originalEvent.currentTarget;
          const containerTop = containerRef.current.getBoundingClientRect().top;
          const rowTop = rowElement.getBoundingClientRect().top;
          const key = e.data.key;
          const y = rowTop - containerTop + rowElement.offsetHeight / 2;
        
          setHoverY(y); 
        
          if (hoverDelayRef.current) clearTimeout(hoverDelayRef.current);
        
          hoverDelayRef.current = setTimeout(() => {
            setHoveredRowKey(key); 
          }, 250);
        }}           
        dataKey="key"
        style={{ width: "100%" }} 
        tableStyle={{ minWidth: "100%" }}
        scrollable
        scrollHeight="flex"
        paginator
        rows={50}
        rowsPerPageOptions={[10, 25, 50, 100, 250, 500]} 
        paginatorTemplate="RowsPerPageDropdown FirstPageLink PrevPageLink CurrentPageReport NextPageLink LastPageLink"
        currentPageReportTemplate="{first} to {last} of {totalRecords}" 
        resizableColumns
        columnResizeMode="expand"
        filters={filters}
        globalFilterFields={[
          "contact_name",
          "contact_email",
          "client_name",
          "account_name",
        ]}
        emptyMessage="No contacts found"
        exportFilename="verity-panel"
        showGridlines
      >
        <Column
          selectionMode="multiple"
          headerStyle={{ width: "75px" }}
          header="Include"
          style={{ width: "75px" }}
          frozen={!isMobile}
          alignFrozen="left" />
        <Column 
          field="internal_id" 
          exportable={false}
          hidden />
        <Column 
          field="in_round" 
          exportable={false} 
          hidden />
        <Column
          field="selected"
          exportHeader="Included"
          hidden
        />
        <Column 
          field="contact_name" 
          header="Name" 
          sortable
          frozen={!isMobile}
          alignFrozen="left" />
        <Column 
          field="contact_email" 
          header="Email" 
          sortable
          frozen={!isMobile}
          alignFrozen="left" />
        { vertical === 'mfv' && 
          <Column 
            field="client_organisation_level_3_name" 
            header="Sub-Division" 
            sortable /> }
        { vertical === 'mfv' && 
          <Column 
            field="client_organisation_level_4_name" 
            header="Sub-Segment 1" 
            sortable /> }
        { vertical === 'mfv' && 
          <Column
            field="client_organisation_level_5_name" 
            header="Sub-Segment 2" 
            sortable /> }
        { vertical === 'mfv' && 
          <Column 
            key="team_name" 
            field="team_name"
            header="Customer Group" 
            sortable /> }
        <Column 
          field="account_name" 
          header="Account" 
          sortable />
        <Column 
          field="contact_division"
           header="Account Division" 
           sortable />
        <Column 
          field="client_name" 
          header={translate(vertical, 'office')} 
          style={{ textTransform: 'capitalize' }} 
          sortable />
        <Column 
          field="contact_job_title" 
          header="Job Title" 
          sortable />
        { vertical === 'mfv' && 
          <Column 
            field="contact_job_function" 
            header="Job Function" 
            sortable /> }
        { vertical === 'mfv' && 
          <Column 
            field="contact_location" 
            header="Contact Market" 
            sortable /> }
        { vertical === 'mfv' && 
          <Column 
            field="contact_account_site" 
            header="Account Site" 
            sortable /> }
        <Column 
          field="contact_type" 
          header="Type" 
          sortable />
        <Column 
          field="contact_seniority"
          header="Seniority" 
          sortable />
        <Column 
          field="contact_language"
          header="Language" 
          sortable />
        <Column
          field="serial_non_responder"
          header="Non-Responder"
          body={nonResponderTemplate}
          sortable />
        <Column 
          field="survey_name" 
          header="Survey Name" 
          sortable />
      </DataTable>

      <div
        style={{
          top: hoverY,
          transform: "translateY(-50%)",
          pointerEvents: hoveredRowKey ? "auto" : "none",
        }}
        className={cn(
          "absolute right-2 z-20 opacity-0 transition-all duration-300 ease-in-out translate-y-1",
          hoveredRowKey && "opacity-100 translate-y-0"
        )}
      >
        <button
          className="px-2 py-1 rounded shadow-md bg-surface-neutral-100 hover:opacity-90 hover:bg-primary transition"
          onClick={() => {
            const row = panel.find((p) => p.key === hoveredRowKey);
            if (row) setPanelMemberSheetState(row);
          }}
        >
          <span className="flex items-center">
            <FilePenLine className="h-3 w-3 mr-1" />
            <span className="text-secondary uppercase text-xxs font-medium">Edit Panel Member</span>
          </span>
        </button>
      </div>
    </div>
  );
};

export default PanelTable;

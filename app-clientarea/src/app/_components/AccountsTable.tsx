"use client";

import { useMemo, useState, useEffect, useRef } from "react";
import { useAccounts } from "@/_providers/AccountsContext";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { Check, ChevronDown, Plus, FilePenLine, Pencil, CircleX, CircleCheck } from "lucide-react";
import Select2, { components } from "react-select";
import AddPanelManager from "@/_components/AddPanelManager";
import AddSignatory from "./AddSignatory";
import EditAccount from "./EditAccount";
import { translate } from "@/_utils/translate";
import { cn } from "@/_utils/tailwind";
import { useMediaQuery } from "react-responsive";
import { useConditionalScroll } from "@/_hooks/useConditionalScroll";


const AccountsTable = ({
  dt,
  dtRef,
  accounts,
  selectedAccounts,
  setSelectedAccounts,
  signatories,
  vertical
}) => {
  const {
    accounts2,
    updateAccount,
    updateSelectedAccounts,
    filters,
    filterPanelManagers,
    setUnsavedChanges,
    accountsVersion
  } = useAccounts();
  const [editAccount, setEditAccount] = useState(null);
  const [panelManager, setPanelManager] = useState("");
  const [signatorySheet, setSignatorySheet] = useState(false);
  const [accountSheet, setAccountSheet] = useState(false);
  const [panelManagerSheet, setPanelManagerSheet] = useState(false);
  const [showLeftShadow, setShowLeftShadow] = useState(false);
  const [showRightShadow, setShowRightShadow] = useState(false);
  const [cellBeingEdited, setCellBeingEdited] = useState<string | null>(null);
  const [cellEditWidths, setCellEditWidths] = useState<{ [key: string]: number }>({});
  const isMobile = useMediaQuery({ query: "(max-width: 1024px)" });
  const DEBOUNCE_THRESHOLD = 200;
  let lastEditTime = 0;

  // Use conditional scroll hook with 150px radius from center
  const { containerRef, isScrollEnabled, showScrollIndicator, scrollIndicatorStyle } = useConditionalScroll({
    radius: 150,
    enabled: !isMobile // Disable on mobile to preserve normal scroll behavior
  });

  useEffect(() => {
    updateSelectedAccounts(selectedAccounts);
  }, []);
  
  useEffect(() => {
    const parent = containerRef.current;
    if (!parent) return;

    // Find the inner scrollable element
    const wrapper = parent.querySelector(".p-datatable-wrapper");
    const table = wrapper?.querySelector("table");
    if (!wrapper) return;
  
    const handleScroll = () => {
      const canScrollLeft = wrapper.scrollLeft > 0;
      const canScrollRight = wrapper.scrollLeft + wrapper.clientWidth < wrapper.scrollWidth - 3;
  
      setShowLeftShadow(false);
      if (isMobile) {
        setShowLeftShadow(canScrollLeft);
      }
      setShowRightShadow(canScrollRight);
    };
  
    handleScroll();
  
    wrapper.addEventListener("scroll", handleScroll);
    window.addEventListener("resize", handleScroll);
    const resizeObserver = new ResizeObserver(handleScroll);

    if (table) {
      resizeObserver.observe(table);
    }
  
    return () => {
      wrapper.removeEventListener("scroll", handleScroll);
      window.removeEventListener("resize", handleScroll);
      resizeObserver.disconnect();
    };
  }, [accounts, isMobile]);  

  const getPanelManager = (panelManagerId) => {
    return filterPanelManagers.find(
      (panelManager) => panelManager.Id === panelManagerId,
    );
  };

  const restructedPanelManagers = useMemo(() => {
    return filterPanelManagers.map((panelManager) => {
      return {
        panel_manager_id: panelManager.Id,
        panel_manager_name: panelManager.Name,
        panel_manager_email: panelManager.Email,
        panel_manager_first_name: panelManager.FirstName,
        panel_manager_last_name: panelManager.LastName,
        value: panelManager.Id,
        label: panelManager.Name,
        Id: panelManager.Id,
        AccountId: panelManager.AccountId,
        ClientHierarchy: panelManager.ClientHierarchy || [],
      };
    });
  }, [filterPanelManagers]);

  const restructuredSignatories = useMemo(() => {
    return signatories.map((signatory) => {
      return {
        signatory_id: signatory.Id,
        signatory_name: signatory.Name,
        signatory_email: signatory.Email,
        value: signatory.Id,
        label: signatory.Name,
        AccountId: signatory.AccountId,
        ClientHierarchy: signatory.ClientHierarchy || [],
      };
    });
  }, [signatories]);

  const setSignatorySheetState = () => {
    setSignatorySheet((state) => !state);
  };

  const setPanelManagertState = (panelManagerId, rowData) => {
    const panelManager = getPanelManager(panelManagerId);
    setPanelManager(panelManager);
    setEditAccount(rowData);
    setPanelManagerSheet((state) => !state);
  };

  const signatoryCellEditTemplate = (options, data) => {
    const cellKey = `${options.rowData.key}-signatory`;
    const filteredSignatories = data.filter(
      (signatory) =>
        signatory.AccountId === options.rowData.client_id ||
        signatory.ClientHierarchy.includes(options.rowData.client_id),
    );
    const currentRowValue =
      options.rowData.signatory?.signatory_id || options.rowData.signatory_id;
    const currentValue = filteredSignatories.filter((contact) => {
      return currentRowValue === contact.signatory_id;
    });

    const SignatorySelectCell = () => {
      useEffect(() => {
        if (cellBeingEdited === cellKey) {
          const input = document.querySelector(`input[id^="react-select"]`);
          if (input) {
            input.focus();
            input.click();
          }
        }
      }, [cellBeingEdited, cellKey]);

      const Control = ({ children, ...props }) => {
        let value = null;
        const selectedValue = props.getValue();

        if (selectedValue.length) {
          value = selectedValue[0].signatory_name;
        } else {
          value = "-";
        }
        return ( 
          <components.Control {...props} className="focus:outline-none focus:ring-0 focus:border-transparent shadow-none">
            <div className="flex w-full items-center justify-between pointer-events-none">
              <span className="truncate">
                {value}
              </span>
              <ChevronDown className="h-4 w-4" />
            </div>
            <div className="absolute inset-0 opacity-0 pointer-events-auto">
              {children}
            </div>
          </components.Control>
        );
      };

      const MenuList = (props) => {
        return (
          <components.MenuList {...props}>
            {props.children}
            <div
              className="flex pb-2 pt-3 mt-1 border-t border-layout-divider justify-center cursor-pointer hover:bg-primary"
              onClick={() => setSignatorySheetState()}
            >
              <Plus className="h-4 w-4 opacity-50 mr-1" />
              <span>Add New</span>
            </div>
          </components.MenuList>
        );
      };

      const Option = (props) => {
        const isSelected = props.isSelected;
        return (
          <components.Option {...props}>
            <div className="flex cursor-pointer">
              {isSelected && <Check className="h-4 w-4 opacity-50" />}
              <span className="text-ellipsis overflow-hidden text-nowrap ml-2">
                {props.data.label}
              </span>
            </div>
          </components.Option>
        );
      };

      return (
        <div className="relative">
            <Select2
              key={cellKey}
              menuIsOpen={cellBeingEdited === cellKey}
              menuPortalTarget={document.body}
              menuPosition="fixed"
              isClearable={false}
              isSearchable={false}
              hideSelectedOptions={false}
              blurInputOnSelect={false}
              defaultValue={currentValue[0]}
              value={currentValue[0]}
              onChange={(value) => {
                options.editorCallback(value);
                dt.current.closeEditingCell();
                setUnsavedChanges(true);
              }}
              onFocus={() => setCellBeingEdited(cellKey)}
              onMenuOpen={() => setCellBeingEdited(cellKey)}
              onMenuClose={() => {
                if (cellBeingEdited === cellKey) {
                  setCellBeingEdited(null);
                  dt.current?.closeEditingCell();
                }
              }}
              options={filteredSignatories}
              noOptionsMessage={() => "No signatories"}
              components={{
                Control,
                MenuList,
                Option,
                DropdownIndicator: null,
                IndicatorSeparator: null,
              }}
              styles={{
                menu: (base) => ({
                  ...base,
                  borderRadius: 0,
                  fontSize: "14px",
                  zIndex: 9999,
                  width: "250px",
                }),
                control: (base) => ({
                  ...base,
                  minHeight: "1.1rem",
                  height: "1.1rem",
                  width: "200px",
                  borderRadius: 0,
                  border: 0,
                  backgroundColor: "transparent",
                  boxShadow: "none",
                  outline: "none",   
                  "&:focus": {
                    boxShadow: "none",
                    outline: "none",
                  },
                  "&:hover": {
                    border: 0,
                  },
                }),
                multiValueLabel: (base) => ({
                  ...base,
                  backgroundColor: "transparent",
                }),
                option: (base) => ({
                  ...base,
                  backgroundColor: "transparent",
                  fontSize: "14px",
                  color: "rgb(49, 52, 55, 0.8)",
                  "&:hover": {
                    backgroundColor: "rgb(242, 231, 66)",
                  },
                  "&:active": {
                    backgroundColor: "rgb(242, 231, 66)",
                  },
                  "&:focus": {
                    backgroundColor: "rgb(242, 231, 66)",
                  },
                }),
                menuPortal: (base) => ({
                  ...base,
                  zIndex: 9999,
                }),
              }}
            />
        </div>
      );
    }

    return <SignatorySelectCell />;
  };

  const panelManagerCellEditTemplate = (options, data) => {
    const rowData = options.rowData;
    const cellKey = `${options.rowData.key}-panelManager`;
    const filteredPanelManagers = data.filter(
      (panelManager) =>
        panelManager.AccountId === options.rowData.client_id ||
        panelManager.ClientHierarchy.includes(options.rowData.client_id) ||
        panelManager.Id.startsWith("NEW"),
    );
    const currentValue = filteredPanelManagers.filter((contact) => {
      return (options.rowData.survey_panel_managers || []).some(
        (val) => val.panel_manager_id === contact.panel_manager_id,
      );
    });

    const PanelManagerSelectCell = () => {
      useEffect(() => {
        if (cellBeingEdited === cellKey) {
          const input = document.querySelector(`input[id^="react-select"]`);
          if (input) {
            input.focus();
            input.click();
          }
        }
      }, [cellBeingEdited, cellKey]);

      const Control = ({ children, ...props }) => {
        let value = "-";
        const selectedValues = props.getValue();

        if (selectedValues.length == 1) {
          value = selectedValues[0].panel_manager_name;
        } else if (selectedValues.length > 1) {
          value = `+${selectedValues.length} Selected`;
        }
        
        return ( 
          <components.Control {...props} className="focus:outline-none focus:ring-0 focus:border-transparent shadow-none">
            <div className="flex w-full items-center justify-between pointer-events-none">
              <span className="truncate leading-none text-sm">
                {value}
              </span>
              <ChevronDown className="h-4 w-4 shrink-0" />
            </div>
            <div className="absolute inset-0 opacity-0 pointer-events-auto">
              {children}
            </div>
          </components.Control>
        );
      };

      const MenuList = (props) => {
        return (
          <components.MenuList {...props}>
            {props.children}
            <div
              className="sticky bottom-0 bg-white border-t border-layout-divider mt-1 px-2 py-4 text-center text-xs text-secondary uppercase hover:underline cursor-pointer"
              onClick={() => setPanelManagertState(null, rowData)}
            >
              <span>Create Panel Manager</span>
            </div>
          </components.MenuList>
        );
      };

      const Option = (props) => {
        const optionValue = props.value;
        const isSelected = props.isSelected;
        return (
          <components.Option {...props}>
            <div className="flex items-center justify-between cursor-pointer">
              <div className="w-4">{isSelected && <Check className="h-4 w-4 opacity-50" />}</div>
              <div className="flex w-[75%]">
                <span className="text-ellipsis overflow-hidden text-nowrap">
                  {props.data.label}
                </span>
              </div>
              <div>
                <FilePenLine
                  className="h-4 w-4 opacity-50 cursor-pointer"
                  onClick={(e) => {
                    e.stopPropagation();
                    setPanelManagertState(optionValue, rowData);
                  }}
                />
              </div>
            </div>
          </components.Option>
        );
      };

      return (
        <div className="relative">
            <Select2
              key={cellKey}
              isMulti
              menuIsOpen={cellBeingEdited === cellKey}
              menuPortalTarget={typeof window !== 'undefined' ? document.body : null}
              menuPosition="fixed"
              isClearable={false}
              isSearchable={false}
              hideSelectedOptions={false}
              blurInputOnSelect={false}
              defaultValue={currentValue}
              noOptionsMessage={() => "No panel managers"}
              value={currentValue}
              onChange={(value) => {
                options.editorCallback(value);
                dt.current.closeEditingCell();
                setUnsavedChanges(true);
              }}
              onFocus={() => setCellBeingEdited(cellKey)}
              onMenuOpen={() => setCellBeingEdited(cellKey)}
              onMenuClose={() => {
                if (cellBeingEdited === cellKey) {
                  setCellBeingEdited(null);
                  dt.current?.closeEditingCell();
                }
              }}
              options={filteredPanelManagers}
              components={{
                Control,
                MenuList,
                Option,
                MultiValue: () => null,
                DropdownIndicator: null,
                IndicatorSeparator: null,
              }}
              styles={{
                menu: (base) => ({
                  ...base,
                  borderRadius: 0,
                  fontSize: "14px",
                  zIndex: 9999,
                  width: "300px",
                }),
                menuList: (base) => ({
                  ...base,
                  padding: 0,
                  margin: 0,
                }),
                control: (base) => ({
                  ...base,
                  minHeight: "1.1rem",
                  height: "1.1rem",
                  width: "200px",
                  lineHeight: "1",
                  padding: 0,
                  borderRadius: 0,
                  border: 0,
                  backgroundColor: "transparent",
                  boxShadow: "none",
                  outline: "none",   
                  "&:focus": {
                    boxShadow: "none",
                    outline: "none",
                  },
                  "&:hover": {
                    border: 0,
                  },
                }),
                multiValue: (base) => ({
                  ...base,
                  maxWidth: "100%",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                }),
                multiValueLabel: (base) => ({
                  ...base,
                  backgroundColor: "transparent",
                }),
                option: (base) => ({
                  ...base,
                  backgroundColor: "transparent",
                  fontSize: "14px",
                  color: "rgb(49, 52, 55, 0.8)",
                  "&:hover": {
                    backgroundColor: "rgb(242, 231, 66)",
                  },
                  "&:active": {
                    backgroundColor: "rgb(242, 231, 66)",
                  },
                  "&:focus": {
                    backgroundColor: "rgb(242, 231, 66)",
                  },
                }),
                menuPortal: (base) => ({
                  ...base,
                  zIndex: 9999,
                }),
              }}
            />
        </div>
      );
    }

    return <PanelManagerSelectCell />;
  };

  const panelManagerTemplate = (row, restructedPanelManagers) => {
    const filteredPanelManagers = restructedPanelManagers.filter(
      (panelManager) =>
        (row.survey_panel_managers || []).some(
          (val) => val.panel_manager_id === panelManager.panel_manager_id
        )
    );
  
    let value = "-";
    if (filteredPanelManagers.length === 1) {
      value = filteredPanelManagers[0].panel_manager_name;
    } else if (filteredPanelManagers.length > 1) {
      value = `+${filteredPanelManagers.length} Selected`;
    }

    return (
      <div className="flex w-[200px] cursor-pointer">
        <div className="flex-1">{value}</div>
        <div>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </div>
      </div>
    );
  };

  const surveyCellEditTemplate = (options) => {
    const cellKey = `${options.rowData.key}-survey_name`;

    const SurveyInput = ({ cellKey }) => {
      const inputRef = useRef(null);
      const originalValue = useRef(options.value);
      const cancelClicked = useRef(false);
      const [value, setValue] = useState(options.value ?? "");
  
      useEffect(() => {
        setTimeout(() => {
          inputRef.current?.focus();
        }, 10);
      }, []);
  
      const saveValue = () => {
        options.editorCallback(value.trim());
        dt.current?.closeEditingCell();
      };
  
      const cancelEdit = () => {
        setTimeout(() => {
          setValue(originalValue.current);
          options.editorCallback(originalValue.current);
          dt.current?.closeEditingCell();
        }, 0);
      };
  
      const handleKeyDown = (e) => {
        if (e.key === "Enter") {
          saveValue();
        } else if (e.key === "Escape") {
          cancelEdit(); 
        }
      };
  
      const handleBlur = () => {
        if (cancelClicked.current) {
          cancelClicked.current = false;
          return;
        }
        saveValue();
      };
  
      return (
        <div className="group flex items-center justify-between h-[1.1rem]" style={{ width: cellEditWidths[cellKey] ? `${cellEditWidths[cellKey]}px` : undefined }}>
          <input
            ref={inputRef}
            type="text"
            value={value}
            onChange={(e) => setValue(e.target.value)}
            onKeyDown={handleKeyDown}
            onBlur={handleBlur}
            className="w-full h-full px-0 py-0 border-0 border-transparent text-sm leading-none bg-transparent outline-none focus:ring-0 focus:outline-none italic text-surface-neutral-400"
          />
          <div
            className="ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1 rounded-full hover:bg-destructive/10 cursor-pointer"
            onMouseDown={() => {
              cancelClicked.current = true;
            }}
            onClick={() => {
              cancelEdit();
            }}
          >
            <CircleX className="h-4 w-4 text-error opacity-60 hover:opacity-100" />
          </div>
        </div>
      );
    };
  
    return <SurveyInput cellKey={cellKey} />;
  };

  const surveyTemplate = (row) => {
    const value = row.survey_name || "-";
  
    return (
      <div id={`survey-display-${row.key}`} className="group flex min-w-0 w-full items-center justify-between relative">
        <span className="truncate w-full">{value}</span>
  
        <div className="absolute right-0 top-[20px] translate-y-[-100%] group-hover:translate-y-[-110%] transition-transform duration-200 cursor-pointer">
          <div className="flex items-center bg-white rounded-md shadow-sm px-2 py-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex-shrink-0">
            <Pencil className="h-3 w-3 text-gray-600 transition-opacity duration-200" />
            <span className="ml-2 text-xxs uppercase font-semibold text-gray-600 transform translate-x-2 opacity-0 group-hover:translate-x-0 group-hover:opacity-100 transition-all duration-200">
              Edit
            </span>
          </div>
        </div>
      </div>
    );
  };

  const signatoryTemplate = (row) => {
    let value = null;
    const signatory = row.signatory_name;

    if (signatory) {
      value = signatory;
    } else {
      value = "-";
    }

    return (
      <div className="flex w-[200px] cursor-pointer">
        <div className="flex-1">{value}</div>
        <div>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </div>
      </div>
    );
  };

  const onSignatoryEditComplete = (e) => {
    let { rowData, newValue, field, originalEvent: event } = e;
    if (newValue) {
      rowData.signatory_name = newValue.signatory_name;
      rowData.signatory_email = newValue.signatory_email;
      rowData.signatory_id = newValue.signatory_id;
    } else event.preventDefault();
    
    // if rowData now has at least 1 panel manager and a signatory_id, remove __isInvalid__ property
    if (
      rowData.survey_panel_managers.length > 0 &&
      rowData.signatory_id !== null
    ) {
      delete rowData.__isInvalid__;
    }

    updateAccount(rowData);
  };

  const onPanelManagerEditComplete = (e) => {
    let { rowData, newValue, field, originalEvent: event } = e;
    if (newValue) {
      rowData.survey_panel_manager_email = newValue
        .map((val) => val.panel_manager_email)
        .join("|");
      rowData.survey_panel_managers = newValue.map((val) => {
        return {
          panel_manager_id: val.panel_manager_id,
          panel_manager_name: val.panel_manager_name,
          panel_manager_email: val.panel_manager_email,
          panel_manager_first_name: val.panel_manager_first_name,
          panel_manager_last_name: val.panel_manager_last_name,
        };
      });
    } else event.preventDefault();
    
    // if rowData now has at least 1 panel manager and a signatory_id and a survey name remove __isInvalid__ property
    if (
      rowData.survey_panel_managers.length > 0 &&
      rowData.signatory_id !== null &&
      (rowData.survey_name !== null && rowData.survey_name !== undefined && rowData.survey_name !== "" && rowData.survey_name.length > 0)
    ) {
      delete rowData.__isInvalid__;
    }

    updateAccount(rowData);
  };

  const onSurveyEditComplete = (e) => {
    const { rowData, newValue, originalEvent: event } = e;
    if (newValue !== undefined && newValue !== null) {
      rowData.survey_name = newValue.trim();
      updateAccount(rowData);
      setUnsavedChanges(true);
    } else event.preventDefault();

    // if rowData now has at least 1 panel manager and a signatory_id and a survey name remove __isInvalid__ property
    if (
      rowData.survey_panel_managers.length > 0 &&
      rowData.signatory_id !== null &&
      (rowData.survey_name !== null && rowData.survey_name !== undefined && rowData.survey_name !== "" && rowData.survey_name.length > 0)
    ) {
      delete rowData.__isInvalid__;
    }
  };

  const onCellEditInit = (cellKey: string, rowKey) => {
    const now = Date.now();
    if (now - lastEditTime < DEBOUNCE_THRESHOLD) return;
    lastEditTime = now;

    const el = document.getElementById(`survey-display-${rowKey}`);
    if (el) {
      const width = el.getBoundingClientRect().width;
      setCellEditWidths((prev) => ({ ...prev, [cellKey]: width }));
    }

    if (cellBeingEdited === cellKey) return;
    
    setCellBeingEdited(null);
    setTimeout(() => setCellBeingEdited(cellKey), 10);
  };

  const onRowSelect = (event) => {
    const account = event.data;
    account.selected = true;
    updateAccount(account);
  };

  const onRowUnselect = (event) => {
    const account = event.data;
    account.selected = false;
    if (account.__isInvalid__) {
      delete account.__isInvalid__;
    }
    updateAccount(account);
  };

  const updateInternalAccounts = (value) => {
    const filteredAccounts = accounts2.filter(
      (account) => account.selected === true,
    );
    setSelectedAccounts(filteredAccounts);
    updateSelectedAccounts(filteredAccounts);
    setUnsavedChanges(true);
  };

  return (
    <div
      ref={containerRef}
      className={cn(
        "relative overflow-x-auto",
        showLeftShadow && "has-left-shadow",
        showRightShadow && "has-right-shadow"
      )}
    >
      <AddSignatory
        openState={signatorySheet}
        setOpenState={setSignatorySheet}
      />

      <AddPanelManager
        panelManager={panelManager}
        account={editAccount}
        openState={panelManagerSheet}
        setOpenState={setPanelManagerSheet}
      />

      <EditAccount
        account={editAccount}
        openState={accountSheet}
        setOpenState={setAccountSheet}
      />

      {/* Scroll indicator */}
      {showScrollIndicator && !isMobile && (
        <div style={scrollIndicatorStyle}>
          {isScrollEnabled ? 'Scroll\nhere' : 'Move cursor\nto center'}
        </div>
      )}

      <DataTable
        ref={dtRef}
        key={accountsVersion}
        value={accounts}
        selectionMode="checkbox"
        selection={selectedAccounts}
        onSelectionChange={(e) => updateInternalAccounts(e.value)}
        onRowSelect={onRowSelect}
        onRowUnselect={onRowUnselect}
        dataKey="key"
        style={{ width: "100%" }} 
        tableStyle={{ minWidth: "100%", tableLayout: "auto" }}
        scrollable
        scrollHeight="flex"
        paginator
        rows={50}
        rowsPerPageOptions={[10, 25, 50, 100, 250, 500]} 
        paginatorTemplate="RowsPerPageDropdown FirstPageLink PrevPageLink CurrentPageReport NextPageLink LastPageLink"
        currentPageReportTemplate="{first} to {last} of {totalRecords}" 
        resizableColumns
        columnResizeMode="expand"
        filters={filters}
        globalFilterFields={["account_name", "client_name"]}
        emptyMessage="No accounts found"
        exportFilename="verity-accounts"
        showGridlines
        rowClassName={(rowData) =>
          rowData.__isInvalid__ ? "invalid" : ""
        }
      >
        <Column
          selectionMode="multiple"
          headerStyle={{ width: "75px" }}
          header="Include"
          style={{ width: "75px" }}
          frozen={!isMobile}
          alignFrozen="left" />
        <Column 
          field="internal_id" 
          hidden />
        <Column 
          field="in_round" 
          exportable={false} 
          hidden />
        <Column
          field="survey_panel_manager_email"
          exportable={false}
          hidden />
        <Column
          field="selected"
          exportHeader="Included"
          hidden
        />
        <Column
          key="account_name"
          field="account_name"
          header="Account"
          headerStyle={{ whiteSpace: "nowrap" }}
          bodyClassName="whitespace-nowrap overflow-hidden text-ellipsis"
          sortable
          frozen={!isMobile}
          alignFrozen="left" />
        <Column 
          key="client_name" 
          field="client_name" 
          header={translate(vertical, 'office')} 
          headerStyle={{ whiteSpace: "nowrap", textTransform: "capitalize" }} 
          bodyClassName="whitespace-nowrap overflow-hidden text-ellipsis"
          sortable
          frozen={!isMobile}
          alignFrozen="left" />
        { vertical === 'mfv' && 
          <Column 
            key="team_name" 
            field="team_name" 
            header="Customer Group" 
            headerStyle={{ whiteSpace: "nowrap" }} 
            sortable /> }
        <Column
          key="client_market"
          field="client_market"
          header={translate(vertical, 'client_market')}
          headerStyle={{ whiteSpace: "nowrap" }}
          bodyClassName="whitespace-nowrap overflow-hidden text-ellipsis"
          sortable />
        { vertical === 'mfv' && 
          <Column 
            field="client_organisation_level_3_name" 
            header="Sub-Division" 
            headerStyle={{ whiteSpace: "nowrap" }} 
            bodyClassName="whitespace-nowrap overflow-hidden text-ellipsis"
            sortable /> }
        { vertical === 'mfv' && 
          <Column 
            field="client_organisation_level_4_name" 
            header="Sub-Segment 1" 
            headerStyle={{ whiteSpace: "nowrap" }} 
            bodyClassName="whitespace-nowrap overflow-hidden text-ellipsis"
            sortable /> }
        { vertical === 'mfv' && 
          <Column 
            field="client_organisation_level_5_name" 
            header="Sub-Segment 2"
            headerStyle={{ whiteSpace: "nowrap" }} 
            bodyClassName="whitespace-nowrap overflow-hidden text-ellipsis"
            sortable /> }
        <Column
          key="signatory"
          field="signatory"
          header="Signatory"
          exportField="signatory_email"
          headerStyle={{ whiteSpace: "nowrap" }}
          style={{ minWidth: "200px" }}
          body={(row) => signatoryTemplate(row)}
          editor={(options) =>
            signatoryCellEditTemplate(options, restructuredSignatories)
          }
          onCellEditInit={(e) => onCellEditInit(`${e.rowData.key}-signatory`, e.rowData.key)}
          onCellEditComplete={onSignatoryEditComplete} />
        <Column
          key="survey_panel_managers"
          field="survey_panel_managers"
          header="Panel Managers"
          exportField="survey_panel_manager_email"
          headerStyle={{ whiteSpace: "nowrap" }}
          style={{ minWidth: "200px" }}
          body={(row) => panelManagerTemplate(row, restructedPanelManagers)}
          editor={(options) =>
            panelManagerCellEditTemplate(options, restructedPanelManagers)
          }
          onCellEditInit={(e) => onCellEditInit(`${e.rowData.key}-panelManager`, e.rowData.key)}
          onCellEditComplete={onPanelManagerEditComplete} />
        <Column
          field="survey_name"
          header="Survey Name"
          headerStyle={{ whiteSpace: "nowrap" }}
          style={{ minWidth: "200px" }}
          body={surveyTemplate}
          bodyClassName="whitespace-nowrap overflow-hidden text-ellipsis"
          sortable
          editor={surveyCellEditTemplate}
          cellEditValidator={(e) => false}
          onCellEditInit={(e) => onCellEditInit(`${e.rowData.key}-survey_name`, e.rowData.key)}
          onCellEditComplete={onSurveyEditComplete}
        />
      </DataTable>
    </div>
  );
};

export default AccountsTable;

"use client";

import { useState, useRef, useEffect } from "react";
import { usePanel } from "@/_providers/PanelContext";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { translate } from "@/_utils/translate";
import { cn } from "@/_utils/tailwind";
import { useConditionalScroll } from "@/_hooks/useConditionalScroll";
import { useMediaQuery } from "react-responsive";


const PanelMembersViewByTableReadOnly = ({ dtCallback, vertical }) => {
  const { panel, filters } = usePanel();
  const [showLeftShadow, setShowLeftShadow] = useState(false);
  const [showRightShadow, setShowRightShadow] = useState(false);
  const isMobile = useMediaQuery({ query: "(max-width: 1024px)" });

  // Use conditional scroll hook with 150px radius from center
  const { containerRef, isScrollEnabled, showScrollIndicator, scrollIndicatorStyle } = useConditionalScroll({
    radius: 150,
    enabled: !isMobile // Disable on mobile to preserve normal scroll behavior
  });

  useEffect(() => {
    const parent = containerRef.current;
    if (!parent) return;

    // Find the inner scrollable element
    const el = parent.querySelector(".p-datatable-wrapper");
    if (!el) return;
  
    const handleScroll = () => {
      const canScrollLeft = el.scrollLeft > 0;
      const canScrollRight = el.scrollLeft + el.clientWidth < el.scrollWidth - 3;
  
      setShowLeftShadow(canScrollLeft);
      setShowRightShadow(canScrollRight);
    };
  
    handleScroll();
  
    el.addEventListener("scroll", handleScroll);
    window.addEventListener("resize", handleScroll);
  
    return () => {
      el.removeEventListener("scroll", handleScroll);
      window.removeEventListener("resize", handleScroll);
    };
  }, []); 

  const statusTemplate = (status, label) => {
    return (
      <>
        {status === true && (
          <div className="flex items-center">
            <span className="h-2 w-2 rounded-full mr-2 bg-error"></span>
            <span>{label}</span>
          </div>
        )}
      </>
    );
  };

  return (
    <div className="bg-surface-neutral-100 mb-10">
      <div ref={containerRef}
        className={cn(
          "relative overflow-x-auto",
          showLeftShadow && "has-left-shadow",
          showRightShadow && "has-right-shadow"
      )}>
        {/* Scroll indicator */}
        {showScrollIndicator && !isMobile && (
          <div style={scrollIndicatorStyle}>
            {isScrollEnabled ? 'Scroll\nhere' : 'Move cursor\nto center'}
          </div>
        )}

        <DataTable
          ref={dtCallback}
          value={panel}
          dataKey="key"
          style={{ width: "100%" }} 
          tableStyle={{ minWidth: "100%" }}
          scrollable
          scrollHeight="flex"
          paginator
          rows={50}
          rowsPerPageOptions={[10, 25, 50, 100, 250, 500]} 
          paginatorTemplate="RowsPerPageDropdown FirstPageLink PrevPageLink CurrentPageReport NextPageLink LastPageLink"
          currentPageReportTemplate="{first} to {last} of {totalRecords}" 
          resizableColumns
          columnResizeMode="expand"
          filters={filters}
          globalFilterFields={[
            "contact_name",
            "contact_email",
            "client_name",
            "account_name",
          ]}
          emptyMessage="No contacts found"
          exportFilename="verity-panel"
          showGridlines
        >
          <Column 
            field="contact_name" 
            header="Name" 
            sortable />
          <Column 
            field="contact_email" 
            header="Email" 
            sortable />
          { vertical === 'mfv' && 
            <Column 
              field="client_organisation_level_3_name" 
              header="Sub-Division" 
              sortable /> }
          { vertical === 'mfv' && 
            <Column 
              field="client_organisation_level_4_name" 
              header="Sub-Segment 1" 
              sortable /> }
          { vertical === 'mfv' && 
            <Column 
              field="client_organisation_level_5_name" 
              header="Sub-Segment 2" 
              sortable /> }
          { vertical === 'mfv' && 
            <Column 
              key="team_name" 
              field="team_name" 
              header="Customer Group" 
              sortable /> }
          <Column 
            field="account_name" 
            header="Account" 
            sortable />
          <Column 
            field="contact_division" 
            header="Account Division" 
            sortable />
          <Column 
            field="client_name" 
            header={translate(vertical, 'office')} 
            style={{ textTransform: 'capitalize' }}
            sortable />
          <Column 
            field="contact_job_title" 
            header="Job Title" 
            sortable />
          { vertical === 'mfv' && 
            <Column 
              field="contact_job_function" 
              header="Job Function" 
              sortable /> }
          { vertical === 'mfv' && 
            <Column 
              field="contact_location" 
              header="Contact Market" 
              sortable /> }
          { vertical === 'mfv' && 
            <Column 
              field="contact_account_site" 
              header="Account Site" 
              sortable /> }
          <Column 
            field="contact_type" 
            header="Type" 
            sortable />
          <Column 
            field="contact_seniority" 
            header="Seniority" 
            sortable />
          <Column
            field="response_status"
            header="Responded"
            body={(row) => statusTemplate(row.response_status, "Responded")}
            sortable />
          <Column
            field="bounced_status"
            header="Bounced"
            body={(row) => statusTemplate(row.bounced_status, "Bounced")}
            sortable />
          <Column 
            field="survey_name" 
            header="Survey Name" 
            sortable />
        </DataTable>
      </div>

      <div className="absolute bottom-0 left-0 w-full py-3 px-6 bg-surface-neutral-300 uppercase font-medium text-sm">
        Contacts Selected: {panel.length}
      </div>
    </div>
  );
};

export default PanelMembersViewByTableReadOnly;

"use client";

import { useEffect, useRef, useState } from "react";
import { useAccounts } from "@/_providers/AccountsContext";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { translate } from "@/_utils/translate";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/_components/ui/hover-card";
import { cn } from "@/_utils/tailwind";
import { useConditionalScroll } from "@/_hooks/useConditionalScroll";
import { useMediaQuery } from "react-responsive";


const AccountsViewByTableReadOnly = ({ dtCallback, vertical }) => {
  const { accounts2, filters } = useAccounts();
  const [showLeftShadow, setShowLeftShadow] = useState(false);
  const [showRightShadow, setShowRightShadow] = useState(false);
  const isMobile = useMediaQuery({ query: "(max-width: 1024px)" });

  // Use conditional scroll hook with 150px radius from center
  const { containerRef, isScrollEnabled, showScrollIndicator, scrollIndicatorStyle } = useConditionalScroll({
    radius: 150,
    enabled: !isMobile // Disable on mobile to preserve normal scroll behavior
  });

  useEffect(() => {
    const parent = containerRef.current;
    if (!parent) return;

    // Find the inner scrollable element
    const el = parent.querySelector(".p-datatable-wrapper");
    if (!el) return;
  
    const handleScroll = () => {
      const canScrollLeft = el.scrollLeft > 0;
      const canScrollRight = el.scrollLeft + el.clientWidth < el.scrollWidth - 3;
  
      setShowLeftShadow(canScrollLeft);
      setShowRightShadow(canScrollRight);
    };
  
    handleScroll();
  
    el.addEventListener("scroll", handleScroll);
    window.addEventListener("resize", handleScroll);
  
    return () => {
      el.removeEventListener("scroll", handleScroll);
      window.removeEventListener("resize", handleScroll);
    };
  }, []); 

  const panelManagerTemplate = (row) => {
    let value = null;
    const panel_managers = row.survey_panel_managers;

    if (panel_managers.length == 0) {
      value = "";
    } else if (panel_managers.length == 1) {
      value = panel_managers[0].panel_manager_name;
    } else {
      value = (
        <HoverCard>
          <HoverCardTrigger>
            <div className="cursor-pointer">
                {`+${panel_managers.length} Selected`}
            </div>
          </HoverCardTrigger>
          <HoverCardContent>
            <p className="font-semibold mb-2">Selected Panel Managers</p>
            <hr className="bg-surface-neutral-300 border-0 h-px mb-2" />
            <ul className="space-y-1">
              {panel_managers.map((panel_manager) => (
                <li key={panel_manager.panel_manager_id}>
                  {panel_manager.panel_manager_name}
                </li>
              ))}
            </ul>
          </HoverCardContent>
        </HoverCard>
      )
    }

    return <div>{value}</div>
  };

  return (
    <div className="bg-surface-neutral-100 mb-10">
      <div ref={containerRef}
        className={cn(
          "relative overflow-x-auto",
          showLeftShadow && "has-left-shadow",
          showRightShadow && "has-right-shadow"
      )}>
        {/* Scroll indicator */}
        {showScrollIndicator && !isMobile && (
          <div style={scrollIndicatorStyle}>
            Scroll{'\n'}here
          </div>
        )}

        <DataTable
          ref={dtCallback}
          value={accounts2}
          dataKey="key"
          style={{ width: "100%" }} 
          tableStyle={{ minWidth: "100%" }}
          scrollable
          scrollHeight="flex"
          paginator
          rows={50}
          rowsPerPageOptions={[10, 25, 50, 100, 250, 500]} 
          paginatorTemplate="RowsPerPageDropdown FirstPageLink PrevPageLink CurrentPageReport NextPageLink LastPageLink"
          currentPageReportTemplate="{first} to {last} of {totalRecords}" 
          resizableColumns
          columnResizeMode="expand"
          filters={filters}
          globalFilterFields={["account_name", "client_name"]}
          emptyMessage="No accounts found"
          exportFilename="verity-accounts"
          showGridlines
        >
          <Column
            field="survey_panel_manager_email"
            exportable={false}
            hidden />
          <Column 
            field="account_name" 
            header="Account"
            headerStyle={{ whiteSpace: "nowrap" }}
            bodyClassName="whitespace-nowrap overflow-hidden text-ellipsis"
            sortable />
          <Column 
            field="client_name" 
            header={translate(vertical, 'office')} 
            headerStyle={{ whiteSpace: "nowrap", textTransform: "capitalize" }}
            bodyClassName="whitespace-nowrap overflow-hidden text-ellipsis"
            sortable />
          { vertical === 'mfv' && 
            <Column 
              key="team_name" 
              field="team_name" 
              header="Customer Group" 
              headerStyle={{ whiteSpace: "nowrap" }}
              bodyClassName="whitespace-nowrap overflow-hidden text-ellipsis"
              sortable /> }
          <Column 
            field="client_market" 
            header="Market" 
            headerStyle={{ whiteSpace: "nowrap" }}
            bodyClassName="whitespace-nowrap overflow-hidden text-ellipsis"
            sortable />
          { vertical === 'mfv' && 
            <Column 
              field="client_organisation_level_3_name" 
              header="Sub-Division" 
              headerStyle={{ whiteSpace: "nowrap" }}
              bodyClassName="whitespace-nowrap overflow-hidden text-ellipsis"
              sortable /> }
          { vertical === 'mfv' && 
            <Column 
              field="client_organisation_level_4_name" 
              header="Sub-Segment 1" 
              headerStyle={{ whiteSpace: "nowrap" }}
              bodyClassName="whitespace-nowrap overflow-hidden text-ellipsis"
              sortable /> }
          { vertical === 'mfv' && 
            <Column 
              field="client_organisation_level_5_name" 
              header="Sub-Segment 2" 
              headerStyle={{ whiteSpace: "nowrap" }}
              bodyClassName="whitespace-nowrap overflow-hidden text-ellipsis"
              sortable /> }
          <Column
            field="signatory_name"
            header="Signatory"
            headerStyle={{ whiteSpace: "nowrap" }}
            bodyClassName="whitespace-nowrap overflow-hidden text-ellipsis"
            sortable />
          <Column
            field="survey_panel_managers"
            header="Panel Managers"
            exportField="survey_panel_manager_email"
            headerStyle={{ whiteSpace: "nowrap" }}
            bodyClassName="whitespace-nowrap overflow-hidden text-ellipsis"
            body={(row) => panelManagerTemplate(row)}
            sortable />
          <Column
            field="survey_round"
            header="Survey Round"
            headerStyle={{ whiteSpace: "nowrap" }}
            bodyClassName="whitespace-nowrap overflow-hidden text-ellipsis"
            sortable />
          <Column
            field="survey_name"
            header="Survey Name"
            headerStyle={{ whiteSpace: "nowrap" }}
            bodyClassName="whitespace-nowrap overflow-hidden text-ellipsis"
            sortable />
          <Column
            field="user_selected_account"
            exportable={false}
            hidden />
        </DataTable>
      </div>

      <div className="absolute bottom-0 left-0 w-full py-3 px-6 bg-surface-neutral-300 uppercase font-medium text-sm">
        Accounts Selected: {accounts2.length}
      </div>
    </div>
  );
};

export default AccountsViewByTableReadOnly;
